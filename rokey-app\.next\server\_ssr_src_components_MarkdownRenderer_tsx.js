"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_MarkdownRenderer_tsx";
exports.ids = ["_ssr_src_components_MarkdownRenderer_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CopyButton */ \"(ssr)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_2__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n}\n// Dynamic imports to isolate problematic packages with error handling\nconst ReactMarkdown = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-markdown\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse bg-gray-100 rounded p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded w-1/2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined)\n});\n// Dynamic import for syntax highlighter with fallback\nconst SyntaxHighlighter = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-syntax-highlighter\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                children: \"Loading syntax highlighter...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, undefined)\n});\nconst syntaxHighlighterStyle = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(/*! react-syntax-highlighter/dist/esm/styles/prism */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js\");\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-syntax-highlighter/dist/esm/styles/prism\"\n        ]\n    },\n    ssr: false\n});\n// Enhanced syntax highlighter with proper colors\nconst EnhancedSyntaxHighlighter = ({ children, language })=>{\n    const [style, setStyle] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"EnhancedSyntaxHighlighter.useEffect\": ()=>{\n            // Load the style dynamically\n            Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/react-syntax-highlighter\"), __webpack_require__.e(1)]).then(__webpack_require__.bind(__webpack_require__, /*! react-syntax-highlighter/dist/esm/styles/prism */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js\")).then({\n                \"EnhancedSyntaxHighlighter.useEffect\": (mod)=>{\n                    setStyle(mod.oneDark);\n                }\n            }[\"EnhancedSyntaxHighlighter.useEffect\"]).catch({\n                \"EnhancedSyntaxHighlighter.useEffect\": ()=>{\n                    // Fallback to null if style loading fails\n                    setStyle(null);\n                }\n            }[\"EnhancedSyntaxHighlighter.useEffect\"]);\n        }\n    }[\"EnhancedSyntaxHighlighter.useEffect\"], []);\n    if (style) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SyntaxHighlighter, {\n            style: style,\n            language: language || 'text',\n            PreTag: \"div\",\n            className: \"text-sm\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fallback while loading or if syntax highlighter fails\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\nfunction MarkdownRenderer({ content, className = '' }) {\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `markdown-content ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-gray-900 leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `markdown-content ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactMarkdown, {\n                    remarkPlugins: [],\n                    components: {\n                        // Headers\n                        h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, void 0),\n                        h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, void 0),\n                        h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, void 0),\n                        h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, void 0),\n                        // Paragraphs\n                        p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, void 0),\n                        // Bold and italic\n                        strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, void 0),\n                        em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                className: \"italic text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, void 0),\n                        // Lists\n                        ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, void 0),\n                        ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, void 0),\n                        li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"leading-relaxed text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, void 0),\n                        // Code blocks and inline code\n                        code: ({ node, inline, className, children, ...props })=>{\n                            const match = /language-(\\w+)/.exec(className || '');\n                            const language = match ? match[1] : '';\n                            const codeContent = String(children).replace(/\\n$/, '');\n                            if (!inline) {\n                                // Handle code blocks (both with and without language detection)\n                                if (language) {\n                                    // Code block with simple syntax highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedSyntaxHighlighter, {\n                                                language: language,\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, void 0);\n                                } else {\n                                    // Code block without language (plain text code block)\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                                ...props,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Blockquotes\n                        blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, void 0),\n                        // Links\n                        a: ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, void 0),\n                        // Tables\n                        table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full border border-gray-200 rounded-lg\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, void 0),\n                        thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, void 0),\n                        tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-200 bg-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, void 0),\n                        tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, void 0),\n                        th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, void 0),\n                        td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, void 0),\n                        // Horizontal rule\n                        hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-4 border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, void 0)\n                    },\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MarkdownRenderer.tsx\n");

/***/ })

};
;